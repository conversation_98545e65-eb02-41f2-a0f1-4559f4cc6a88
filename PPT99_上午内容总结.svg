<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #34495e; text-anchor: middle; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; }
      .method-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #7f8c8d; }
      .base-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #3498db; text-anchor: middle; }
      .conclusion-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; fill: #e74c3c; text-anchor: middle; }
      .english-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: #95a5a6; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 主标题 -->
  <text x="960" y="100" class="title-text">上午内容总结</text>
  
  <!-- 天平支点 -->
  <g transform="translate(960, 600)">
    <!-- 天平底座 -->
    <rect x="-150" y="150" width="300" height="60" fill="#34495e" rx="10"/>
    <text x="0" y="190" class="base-text">数据驱动</text>
    <text x="0" y="220" class="english-text">(Data-Driven)</text>
    
    <!-- 天平支柱 -->
    <rect x="-10" y="50" width="20" height="100" fill="#34495e"/>
    
    <!-- 天平横梁 -->
    <rect x="-400" y="40" width="800" height="20" fill="#34495e" rx="10"/>
    
    <!-- 左侧天平盘 -->
    <g transform="translate(-300, 50)">
      <ellipse cx="0" cy="0" rx="120" ry="20" fill="#3498db" opacity="0.8"/>
      <rect x="-120" y="-5" width="240" height="10" fill="#2980b9"/>
      
      <!-- 防守内容 -->
      <text x="0" y="-40" class="section-title">防守</text>
      <text x="0" y="-10" class="english-text">(Defense)</text>
      <text x="-100" y="60" class="content-text">核心：客户挽留</text>
      <text x="-100" y="90" class="method-text">方法：流失预警 + 分级干预</text>
    </g>
    
    <!-- 右侧天平盘 -->
    <g transform="translate(300, 50)">
      <ellipse cx="0" cy="0" rx="120" ry="20" fill="#e74c3c" opacity="0.8"/>
      <rect x="-120" y="-5" width="240" height="10" fill="#c0392b"/>
      
      <!-- 进攻内容 -->
      <text x="0" y="-40" class="section-title">进攻</text>
      <text x="0" y="-10" class="english-text">(Offense)</text>
      <text x="-100" y="60" class="content-text">核心：价值提升</text>
      <text x="-100" y="90" class="method-text">方法：向上销售 + 交叉销售</text>
    </g>
    
    <!-- 连接线 -->
    <line x1="-300" y1="30" x2="-20" y2="50" stroke="#7f8c8d" stroke-width="2"/>
    <line x1="300" y1="30" x2="20" y2="50" stroke="#7f8c8d" stroke-width="2"/>
  </g>
  
  <!-- 结论 -->
  <text x="960" y="900" class="conclusion-text">攻守兼备，方能决胜千里</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 980 Q 960 930 1720 980" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
</svg>
