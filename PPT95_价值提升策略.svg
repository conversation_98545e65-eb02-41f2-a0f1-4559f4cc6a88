<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #34495e; }
      .definition-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #2c3e50; }
      .example-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #7f8c8d; }
      .english-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #95a5a6; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 主标题 -->
  <text x="960" y="100" class="title-text">从"防守"到"进攻"：客户价值提升策略</text>
  
  <!-- 左侧向上箭头区域 -->
  <g transform="translate(480, 400)">
    <!-- 向上箭头 -->
    <path d="M 0 -150 L -40 -100 L -20 -100 L -20 100 L 20 100 L 20 -100 L 40 -100 Z" fill="#3498db"/>
    
    <!-- Up-selling标题 -->
    <text x="0" y="180" class="section-title">Up-selling</text>
    <text x="0" y="220" class="english-text">(向上销售)</text>
    
    <!-- 定义 -->
    <text x="-200" y="280" class="definition-text">定义：让他用得"更好"</text>
    <text x="-200" y="320" class="example-text">(引导客户购买同一产品的更高阶版本)</text>
    
    <!-- 示例 -->
    <text x="-200" y="380" class="example-text">示例：4G套餐 → 5G套餐</text>
  </g>
  
  <!-- 右侧向右箭头区域 -->
  <g transform="translate(1440, 400)">
    <!-- 向右箭头 -->
    <path d="M -150 0 L -100 -40 L -100 -20 L 100 -20 L 100 20 L -100 20 L -100 40 Z" fill="#e74c3c"/>
    
    <!-- Cross-selling标题 -->
    <text x="0" y="180" class="section-title">Cross-selling</text>
    <text x="0" y="220" class="english-text">(交叉销售)</text>
    
    <!-- 定义 -->
    <text x="-200" y="280" class="definition-text">定义：让他用得"更广"</text>
    <text x="-200" y="320" class="example-text">(引导客户购买其他相关的产品或服务)</text>
    
    <!-- 示例 -->
    <text x="-200" y="380" class="example-text">示例：手机号用户 → 手机号+宽带+电视用户</text>
  </g>
  
  <!-- 中央连接弧线 -->
  <path d="M 600 500 Q 960 450 1320 500" fill="none" stroke="#95a5a6" stroke-width="3" stroke-dasharray="10,5"/>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 900 Q 960 850 1720 900" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
  <path d="M 300 950 Q 960 900 1620 950" fill="none" stroke="#e74c3c" stroke-width="3" opacity="0.3"/>
</svg>
