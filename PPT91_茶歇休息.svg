<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 72px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #34495e; text-anchor: middle; }
      .time-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #7f8c8d; text-anchor: middle; }
      .coffee-steam { fill: none; stroke: #bdc3c7; stroke-width: 3; opacity: 0.7; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 咖啡杯 -->
  <g transform="translate(960, 400)">
    <!-- 杯子主体 -->
    <ellipse cx="0" cy="80" rx="120" ry="25" fill="#8b4513"/>
    <rect x="-120" y="0" width="240" height="80" fill="#d2691e"/>
    <ellipse cx="0" cy="0" rx="120" ry="25" fill="#daa520"/>
    
    <!-- 咖啡液面 -->
    <ellipse cx="0" cy="10" rx="100" ry="20" fill="#654321"/>
    
    <!-- 杯把手 -->
    <path d="M 120 30 Q 180 30 180 50 Q 180 70 120 70" fill="none" stroke="#8b4513" stroke-width="12"/>
    
    <!-- 蒸汽 -->
    <path class="coffee-steam" d="M -40 -20 Q -35 -40 -40 -60 Q -45 -80 -40 -100">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite"/>
    </path>
    <path class="coffee-steam" d="M 0 -20 Q 5 -40 0 -60 Q -5 -80 0 -100">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2s" repeatCount="indefinite"/>
    </path>
    <path class="coffee-steam" d="M 40 -20 Q 45 -40 40 -60 Q 35 -80 40 -100">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- 主标题 -->
  <text x="960" y="650" class="title-text">短暂休息，准备进入价值提升的核心战役</text>
  
  <!-- 英文副标题 -->
  <text x="960" y="730" class="subtitle-text">Coffee Break</text>
  
  <!-- 时间 -->
  <text x="960" y="820" class="time-text">10:30 - 10:45</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 400 900 Q 960 850 1520 900" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
  <path d="M 500 950 Q 960 900 1420 950" fill="none" stroke="#e74c3c" stroke-width="3" opacity="0.3"/>
</svg>
