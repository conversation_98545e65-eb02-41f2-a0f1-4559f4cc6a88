<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .core-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; fill: #e74c3c; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #34495e; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; }
      .bullet-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #34495e; }
      .interaction-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #3498db; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 侦探剪影背景 -->
  <g transform="translate(1600, 200)" opacity="0.1">
    <!-- 侦探帽 -->
    <ellipse cx="0" cy="-20" rx="60" ry="15" fill="#2c3e50"/>
    <path d="M -40 -20 Q -30 -40 0 -35 Q 30 -40 40 -20" fill="#2c3e50"/>
    
    <!-- 头部 -->
    <circle cx="0" cy="0" r="40" fill="#2c3e50"/>
    
    <!-- 放大镜 -->
    <circle cx="80" cy="20" r="30" fill="none" stroke="#2c3e50" stroke-width="6"/>
    <path d="M 105 45 L 125 65" stroke="#2c3e50" stroke-width="8"/>
  </g>
  
  <!-- 主标题 -->
  <text x="960" y="100" class="title-text">客户挽留的艺术：像侦探一样发现"分手"前兆</text>
  
  <!-- 核心思想 -->
  <text x="960" y="180" class="core-text">成功的挽留，是"治未病"，而非"治已病"</text>
  
  <!-- 关键流失前兆指标库标题 -->
  <text x="150" y="260" class="section-title">关键流失前兆指标库：</text>
  
  <!-- 账户行为 -->
  <g transform="translate(150, 320)">
    <circle cx="0" cy="0" r="8" fill="#3498db"/>
    <text x="30" y="8" class="content-text">账户行为：连续多月ARPU下降、主动关闭长期增值业务…</text>
  </g>
  
  <!-- 通信行为 -->
  <g transform="translate(150, 380)">
    <circle cx="0" cy="0" r="8" fill="#e74c3c"/>
    <text x="30" y="8" class="content-text">通信行为：通话/流量使用量断崖式下跌、网龄长但从未使用APP…</text>
  </g>
  
  <!-- 服务交互行为 -->
  <g transform="translate(150, 440)">
    <circle cx="0" cy="0" r="8" fill="#f39c12"/>
    <text x="30" y="8" class="content-text">服务交互行为：短期内多次投诉、频繁查询"携号转网"流程…</text>
  </g>
  
  <!-- 外部信息 -->
  <g transform="translate(150, 500)">
    <circle cx="0" cy="0" r="8" fill="#9b59b6"/>
    <text x="30" y="8" class="content-text">外部信息：客户所在小区被竞对宽带广告"包围"…</text>
  </g>
  
  <!-- 互动区域 -->
  <g transform="translate(150, 600)">
    <rect x="0" y="0" width="1620" height="120" fill="#ecf0f1" rx="20"/>
    <text x="50" y="50" class="interaction-text">互动：你在工作中，还发现过哪些"分手"信号？</text>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 800 Q 960 750 1820 800" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
  <path d="M 200 850 Q 960 800 1720 850" fill="none" stroke="#e74c3c" stroke-width="3" opacity="0.3"/>
</svg>
