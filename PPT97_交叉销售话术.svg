<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .scene-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #34495e; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3498db; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 主标题 -->
  <text x="960" y="80" class="title-text">交叉销售话术解析：找到"逻辑关联"</text>
  
  <!-- 场景描述 -->
  <g transform="translate(100, 150)">
    <rect x="0" y="0" width="1720" height="80" fill="#e8f4fd" rx="15"/>
    <text x="30" y="35" class="scene-title">场景：客户来营业厅咨询，提到家里老人孩子多</text>
  </g>
  
  <!-- 话术解析标题 -->
  <text x="100" y="290" class="scene-title">话术解析：</text>
  
  <!-- 步骤1 -->
  <g transform="translate(100, 330)">
    <circle cx="20" cy="20" r="15" fill="#3498db"/>
    <text x="15" y="28" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; fill: white; font-weight: bold;">1</text>
    <text x="60" y="15" class="step-title">赞美+建立关系：</text>
    <text x="60" y="45" class="content-text">"陈哥，您是咱们移动十多年的老用户了…"</text>
  </g>
  
  <!-- 步骤2 -->
  <g transform="translate(100, 420)">
    <circle cx="20" cy="20" r="15" fill="#e74c3c"/>
    <text x="15" y="28" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; fill: white; font-weight: bold;">2</text>
    <text x="60" y="15" class="step-title">洞察需求+引发兴趣：</text>
    <text x="60" y="45" class="content-text">"家里老人小孩多，肯定对网络稳定性要求特别高吧？家里现在用的哪家宽带呀？"</text>
  </g>
  
  <!-- 步骤3 -->
  <g transform="translate(100, 510)">
    <circle cx="20" cy="20" r="15" fill="#f39c12"/>
    <text x="15" y="28" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; fill: white; font-weight: bold;">3</text>
    <text x="60" y="15" class="step-title">突出"专属"优势：</text>
    <text x="60" y="45" class="content-text">"像您这样的金牌老用户，办咱们自己的宽带，优惠力度是最大的…"</text>
  </g>
  
  <!-- 步骤4 -->
  <g transform="translate(100, 600)">
    <circle cx="20" cy="20" r="15" fill="#27ae60"/>
    <text x="15" y="28" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; fill: white; font-weight: bold;">4</text>
    <text x="60" y="15" class="step-title">强调"便捷"价值：</text>
    <text x="60" y="45" class="content-text">"手机和宽带用一家，以后缴费、报修，一个电话就全解决了，省心省力。"</text>
  </g>
  
  <!-- 步骤5 -->
  <g transform="translate(100, 690)">
    <circle cx="20" cy="20" r="15" fill="#9b59b6"/>
    <text x="15" y="28" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; fill: white; font-weight: bold;">5</text>
    <text x="60" y="15" class="step-title">低门槛邀约：</text>
    <text x="60" y="45" class="content-text">"要不我帮您查一下您家小区的资源和最新的老用户优惠政策？"</text>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 850 Q 960 800 1820 850" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
  <path d="M 200 900 Q 960 850 1720 900" fill="none" stroke="#e74c3c" stroke-width="3" opacity="0.3"/>
</svg>
