<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #34495e; text-anchor: middle; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #2c3e50; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #7f8c8d; }
      .english-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #95a5a6; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 主标题 -->
  <text x="960" y="120" class="title-text">从"防守"到"进攻"：客户挽留与价值提升</text>
  
  <!-- 左侧防守区域 -->
  <g transform="translate(480, 300)">
    <!-- 盾牌 -->
    <path d="M 0 -80 Q -60 -60 -80 0 Q -60 60 0 80 Q 60 60 80 0 Q 60 -60 0 -80 Z" fill="#3498db" opacity="0.8"/>
    <path d="M 0 -60 Q -40 -45 -60 0 Q -40 45 0 60 Q 40 45 60 0 Q 40 -45 0 -60 Z" fill="#2980b9"/>
    <circle cx="0" cy="0" r="20" fill="#ecf0f1"/>
    
    <!-- 防守标题 -->
    <text x="0" y="150" class="section-title">防守反击</text>
    <text x="0" y="190" class="english-text">(Defense)</text>
    
    <!-- 防守内容 -->
    <text x="-150" y="250" class="content-text">主题：客户流失预警与主动干预</text>
    <text x="-150" y="300" class="content-text">目标：守住我们的"客户资产"基本盘</text>
  </g>
  
  <!-- 右侧进攻区域 -->
  <g transform="translate(1440, 300)">
    <!-- 矛 -->
    <path d="M 0 -80 L -20 -60 L -10 -60 L -10 60 L 10 60 L 10 -60 L 20 -60 Z" fill="#e74c3c"/>
    <path d="M 0 -80 L -15 -50 L 0 -40 L 15 -50 Z" fill="#c0392b"/>
    <ellipse cx="0" cy="70" rx="15" ry="10" fill="#8b4513"/>
    
    <!-- 进攻标题 -->
    <text x="0" y="150" class="section-title">主动进攻</text>
    <text x="0" y="190" class="english-text">(Offense)</text>
    
    <!-- 进攻内容 -->
    <text x="-150" y="250" class="content-text">主题：客户价值提升策略</text>
    <text x="-150" y="300" class="content-text">目标：让"客户资产"保值增值</text>
  </g>
  
  <!-- 中央连接线 -->
  <path d="M 600 400 Q 960 350 1320 400" fill="none" stroke="#95a5a6" stroke-width="3" stroke-dasharray="10,5"/>
  
  <!-- 底部装饰弧线 -->
  <path d="M 200 900 Q 960 850 1720 900" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
  <path d="M 300 950 Q 960 900 1620 950" fill="none" stroke="#e74c3c" stroke-width="3" opacity="0.3"/>
</svg>
