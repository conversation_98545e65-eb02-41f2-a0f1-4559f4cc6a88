<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .level-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #2c3e50; line-height: 1.6; }
      .center-bg { fill: #e74c3c; stroke: #c62828; stroke-width: 3; rx: 20; }
      .level1-bg { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 15; }
      .level2-bg { fill: #f39c12; stroke: #e67e22; stroke-width: 2; rx: 15; }
      .level3-bg { fill: #27ae60; stroke: #229954; stroke-width: 2; rx: 15; }
      .connection-line { stroke: #7f8c8d; stroke-width: 3; fill: none; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">第一天学习内容"知识地图"</text>
  
  <!-- 中心点/原点 -->
  <ellipse cx="960" cy="300" rx="200" ry="80" class="center-bg"/>
  <text x="960" y="280" text-anchor="middle" class="content-text" fill="white" font-weight="bold">中心点/原点 (Why)</text>
  <text x="960" y="310" text-anchor="middle" class="content-text" fill="white">三大挑战 & 三大机遇</text>
  
  <!-- 连接线 -->
  <line x1="760" y1="300" x2="400" y2="450" class="connection-line"/>
  <line x1="960" y1="380" x2="960" y2="550" class="connection-line"/>
  <line x1="1160" y1="300" x2="1520" y2="450" class="connection-line"/>
  
  <!-- 第一层/世界观 -->
  <rect x="200" y="400" width="400" height="200" class="level1-bg"/>
  <text x="400" y="440" text-anchor="middle" class="level-title" fill="white">第一层/世界观 (Mindset)</text>
  <text x="220" y="480" class="content-text" fill="white">• 客户是资产</text>
  <text x="220" y="510" class="content-text" fill="white">• LTV &gt; CAC</text>
  <text x="220" y="540" class="content-text" fill="white">• NPS ≈ Growth</text>
  
  <!-- 第二层/导航图 -->
  <rect x="760" y="500" width="400" height="200" class="level2-bg"/>
  <text x="960" y="540" text-anchor="middle" class="level-title" fill="white">第二层/导航图 (Map)</text>
  <text x="780" y="580" class="content-text" fill="white">AARRR模型</text>
  <text x="780" y="610" class="content-text" fill="white">获客/激活/留存/变现/推荐</text>
  
  <!-- 第三层/工具箱 -->
  <rect x="1320" y="400" width="400" height="200" class="level3-bg"/>
  <text x="1520" y="440" text-anchor="middle" class="level-title" fill="white">第三层/工具箱 (Toolkit)</text>
  <text x="1340" y="480" class="content-text" fill="white">显微镜：RFM模型 & 多维分群</text>
  <text x="1340" y="510" class="content-text" fill="white">共情镜：用户画像 (Persona)</text>
  
  <!-- 连接线到工具详细说明 -->
  <line x1="400" y1="600" x2="400" y2="750" class="connection-line"/>
  <line x1="960" y1="700" x2="960" y2="750" class="connection-line"/>
  <line x1="1520" y1="600" x2="1520" y2="750" class="connection-line"/>
  
  <!-- 详细工具说明 -->
  <rect x="150" y="750" width="500" height="120" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="170" y="780" class="content-text" font-weight="bold">理念转变：</text>
  <text x="170" y="810" class="content-text">从产品思维到客户思维</text>
  <text x="170" y="840" class="content-text">从交易关系到价值共生</text>
  
  <rect x="710" y="750" width="500" height="120" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="730" y="780" class="content-text" font-weight="bold">客户旅程：</text>
  <text x="730" y="810" class="content-text">了解客户在哪个阶段</text>
  <text x="730" y="840" class="content-text">针对性制定运营策略</text>
  
  <rect x="1270" y="750" width="500" height="120" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <text x="1290" y="780" class="content-text" font-weight="bold">精准分析：</text>
  <text x="1290" y="810" class="content-text">科学的客户价值评估</text>
  <text x="1290" y="840" class="content-text">深度的用户需求洞察</text>
  
  <!-- 装饰性元素 -->
  <circle cx="100" cy="200" r="30" fill="#e74c3c" opacity="0.3"/>
  <text x="100" y="210" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">Why</text>
  
  <circle cx="1820" cy="200" r="30" fill="#27ae60" opacity="0.3"/>
  <text x="1820" y="210" text-anchor="middle" class="content-text" fill="#1b5e20" font-size="16px">How</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 120 Q 300 100 400 120" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1520 120 Q 1620 100 1720 120" stroke="#27ae60" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
