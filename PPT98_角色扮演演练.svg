<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .task-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #34495e; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; }
      .scene-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #3498db; }
      .scene-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; }
      .time-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 任务卡片背景 -->
  <rect x="100" y="100" width="1720" height="880" fill="#f8f9fa" stroke="#dee2e6" stroke-width="3" rx="20"/>
  
  <!-- 主标题 -->
  <text x="960" y="180" class="title-text">【角色扮演】价值提升话术演练</text>
  
  <!-- 你的任务 -->
  <text x="150" y="260" class="task-title">你的任务：</text>
  <text x="150" y="310" class="content-text">两人结对，一人扮演"客户"，一人扮演"客户经理"</text>
  
  <!-- 你的剧本 -->
  <text x="150" y="380" class="task-title">你的剧本：（从以下场景中抽取一张）</text>
  
  <!-- 场景A -->
  <g transform="translate(150, 430)">
    <rect x="0" y="0" width="1620" height="140" fill="#e8f4fd" rx="15"/>
    <text x="30" y="40" class="scene-title">场景A (Up-selling)：</text>
    <text x="30" y="75" class="scene-text">客户是ARPU 58元的用户，来营业厅抱怨4G网速慢，</text>
    <text x="30" y="105" class="scene-text">请你将他成功升档到98元的5G套餐。</text>
  </g>
  
  <!-- 场景B -->
  <g transform="translate(150, 600)">
    <rect x="0" y="0" width="1620" height="160" fill="#f0f8e8" rx="15"/>
    <text x="30" y="40" class="scene-title">场景B (Cross-selling)：</text>
    <text x="30" y="75" class="scene-text">客户是移动手机老用户，打电话来咨询宽带业务，但表示XX电信的宽带更便宜，</text>
    <text x="30" y="105" class="scene-text">请你将他成功转化为移动宽带用户。</text>
  </g>
  
  <!-- 时间 -->
  <g transform="translate(150, 800)">
    <rect x="0" y="0" width="1620" height="80" fill="#fff3cd" rx="15"/>
    <text x="30" y="35" class="task-title">时间：</text>
    <text x="150" y="35" class="time-text">10分钟准备与演练</text>
  </g>
  
  <!-- 装饰性元素 -->
  <circle cx="1700" cy="300" r="80" fill="#3498db" opacity="0.1"/>
  <circle cx="1650" cy="500" r="60" fill="#e74c3c" opacity="0.1"/>
  <circle cx="1750" cy="700" r="70" fill="#f39c12" opacity="0.1"/>
</svg>
