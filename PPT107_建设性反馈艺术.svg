<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #34495e; }
      .quote-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; font-style: italic; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3498db; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 对话气泡装饰 -->
  <g transform="translate(1600, 200)" opacity="0.08">
    <!-- 气泡1 -->
    <ellipse cx="0" cy="0" rx="80" ry="50" fill="#3498db"/>
    <path d="M -20 40 L -10 60 L 0 40" fill="#3498db"/>
    
    <!-- 气泡2 -->
    <ellipse cx="-150" cy="100" rx="70" ry="40" fill="#e74c3c"/>
    <path d="M -120 130 L -110 150 L -100 130" fill="#e74c3c"/>
  </g>
  
  <!-- 主标题 -->
  <text x="960" y="100" class="title-text">建设性反馈的艺术</text>
  
  <!-- 提问建议 -->
  <text x="150" y="200" class="section-title">提问建议：</text>
  
  <g transform="translate(150, 250)">
    <rect x="0" y="0" width="1620" height="80" fill="#e8f4fd" rx="15"/>
    <text x="30" y="30" class="quote-text">"我特别好奇，你们当时为什么会选择这个创意？"</text>
    <text x="30" y="60" class="quote-text">"关于执行中的XX困难，你们是如何考虑的？"</text>
  </g>
  
  <!-- 点评建议 -->
  <text x="150" y="400" class="section-title">点评建议：</text>
  
  <!-- 三明治点评法 -->
  <text x="150" y="460" class="section-title">"三明治"点评法：</text>
  
  <!-- 步骤1：肯定 -->
  <g transform="translate(150, 520)">
    <circle cx="20" cy="20" r="15" fill="#27ae60"/>
    <text x="15" y="28" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; fill: white; font-weight: bold;">1</text>
    <text x="60" y="15" class="step-text">肯定：</text>
    <text x="60" y="45" class="content-text">"我特别欣赏你们方案中的……"</text>
  </g>
  
  <!-- 步骤2：建议 -->
  <g transform="translate(150, 620)">
    <circle cx="20" cy="20" r="15" fill="#f39c12"/>
    <text x="15" y="28" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; fill: white; font-weight: bold;">2</text>
    <text x="60" y="15" class="step-text">建议：</text>
    <text x="60" y="45" class="content-text">"如果……可能会更好……"</text>
  </g>
  
  <!-- 步骤3：鼓励 -->
  <g transform="translate(150, 720)">
    <circle cx="20" cy="20" r="15" fill="#3498db"/>
    <text x="15" y="28" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; fill: white; font-weight: bold;">3</text>
    <text x="60" y="15" class="step-text">鼓励：</text>
    <text x="60" y="45" class="content-text">"总的来说，这是一个非常有潜力的方案！"</text>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 900 Q 960 850 1820 900" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
  <path d="M 200 950 Q 960 900 1720 950" fill="none" stroke="#e74c3c" stroke-width="3" opacity="0.3"/>
</svg>
