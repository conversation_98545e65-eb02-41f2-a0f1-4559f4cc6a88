<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .scene-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #34495e; }
      .anchor-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3498db; }
      .quote-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; font-style: italic; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 主标题 -->
  <text x="960" y="80" class="title-text">向上销售话术解析：找到"价值锚点"</text>
  
  <!-- 场景一：体验锚点 -->
  <g transform="translate(100, 180)">
    <rect x="0" y="0" width="1720" height="180" fill="#ecf0f1" rx="15"/>
    <text x="30" y="40" class="scene-title">场景一："体验锚点"</text>
    <text x="30" y="80" class="quote-text">"王总，您这部Mate 60 Pro手机，就好像一辆跑车，但您还在用4G套餐，</text>
    <text x="30" y="110" class="quote-text">等于只在市区开，完全发挥不出性能。升到5G，才算是真正'好马配好鞍'！"</text>
  </g>
  
  <!-- 场景二：痛点锚点 -->
  <g transform="translate(100, 400)">
    <rect x="0" y="0" width="1720" height="180" fill="#e8f5e8" rx="15"/>
    <text x="30" y="40" class="scene-title">场景二："痛点锚点"</text>
    <text x="30" y="80" class="quote-text">"李女士，我注意到您上个月流量在25号就用完了，月底这几天是不是有点</text>
    <text x="30" y="110" class="quote-text">'流量焦虑'呀？我们这款5G套餐，流量直接翻三倍…"</text>
  </g>
  
  <!-- 场景三：权益锚点 -->
  <g transform="translate(100, 620)">
    <rect x="0" y="0" width="1720" height="200" fill="#fef9e7" rx="15"/>
    <text x="30" y="40" class="scene-title">场景三："权益锚点"</text>
    <text x="30" y="80" class="quote-text">"陈哥，您现在88元套餐，每月自己再花15元买视频会员，加起来103元了。</text>
    <text x="30" y="110" class="quote-text">我们这个128元5G套餐，不仅流量多网速快，还直接包含了这个会员。</text>
    <text x="30" y="140" class="quote-text">里外里算，您其实只多花几块钱…"</text>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 900 Q 960 850 1820 900" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
  <path d="M 200 950 Q 960 900 1720 950" fill="none" stroke="#e74c3c" stroke-width="3" opacity="0.3"/>
</svg>
