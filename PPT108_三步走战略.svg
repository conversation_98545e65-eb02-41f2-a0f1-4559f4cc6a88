<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #34495e; }
      .step-subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #7f8c8d; }
      .core-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; }
      .english-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: #95a5a6; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 主标题 -->
  <text x="960" y="80" class="title-text">三日所学，皆成此图：客户经营转型"三步走"战略</text>
  
  <!-- 阶梯图 -->
  <g transform="translate(300, 200)">
    <!-- 第一步 (地基) -->
    <rect x="0" y="400" width="1320" height="200" fill="#3498db" opacity="0.8" rx="15"/>
    <text x="50" y="440" class="step-title">第一步 (地基)：思想破冰，认知统一</text>
    <text x="50" y="480" class="step-subtitle">核心：</text>
    <text x="50" y="520" class="core-text">将"客户是资产"奉为圭臬，将"数据驱动"作为习惯，</text>
    <text x="50" y="550" class="core-text">将"体验为王"视为信仰。</text>
    
    <!-- 第二步 (骨架) -->
    <rect x="100" y="200" width="1120" height="200" fill="#e74c3c" opacity="0.8" rx="15"/>
    <text x="150" y="240" class="step-title">第二步 (骨架)：体系搭建，流程再造</text>
    <text x="150" y="280" class="step-subtitle">核心：</text>
    <text x="150" y="320" class="core-text">系统性地引入NPS、客户旅程地图、AARRR等科学方法论，</text>
    <text x="150" y="350" class="core-text">并搭建起私域运营、分级挽留、精准营销的标准作业流程（SOP）。</text>
    
    <!-- 第三步 (飞轮) -->
    <rect x="200" y="0" width="920" height="200" fill="#f39c12" opacity="0.8" rx="15"/>
    <text x="250" y="40" class="step-title">第三步 (飞轮)：价值闭环，持续优化</text>
    <text x="250" y="80" class="step-subtitle">核心：</text>
    <text x="250" y="120" class="core-text">将所有的运营动作，都与LTV的提升和流失率的下降挂钩，</text>
    <text x="250" y="150" class="core-text">通过数据不断地复盘、迭代、优化，形成一个正向循环的增长飞轮。</text>
  </g>
  
  <!-- 连接箭头 -->
  <g transform="translate(960, 400)">
    <!-- 箭头1 -->
    <path d="M -400 200 L -300 100 L -320 100 L -320 90 L -280 90 L -280 110 L -300 110" fill="#2c3e50"/>
    
    <!-- 箭头2 -->
    <path d="M -200 100 L -100 0 L -120 0 L -120 -10 L -80 -10 L -80 10 L -100 10" fill="#2c3e50"/>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 850 Q 960 800 1820 850" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
  <path d="M 200 900 Q 960 850 1720 900" fill="none" stroke="#e74c3c" stroke-width="3" opacity="0.3"/>
</svg>
