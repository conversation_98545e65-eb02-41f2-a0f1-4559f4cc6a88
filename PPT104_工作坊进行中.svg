<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .timer-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 120px; font-weight: bold; fill: #e74c3c; text-anchor: middle; }
      .tip-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #34495e; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 动态背景数据流 -->
  <g opacity="0.05">
    <!-- 数据流线条 -->
    <path d="M 0 200 Q 480 150 960 200 Q 1440 250 1920 200" fill="none" stroke="#3498db" stroke-width="8">
      <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="3s" repeatCount="indefinite"/>
    </path>
    <path d="M 0 400 Q 480 350 960 400 Q 1440 450 1920 400" fill="none" stroke="#e74c3c" stroke-width="6">
      <animate attributeName="stroke-dasharray" values="100,0;50,50;0,100" dur="4s" repeatCount="indefinite"/>
    </path>
    <path d="M 0 600 Q 480 550 960 600 Q 1440 650 1920 600" fill="none" stroke="#f39c12" stroke-width="4">
      <animate attributeName="stroke-dasharray" values="0,80;40,40;80,0" dur="2.5s" repeatCount="indefinite"/>
    </path>
    <path d="M 0 800 Q 480 750 960 800 Q 1440 850 1920 800" fill="none" stroke="#27ae60" stroke-width="5">
      <animate attributeName="stroke-dasharray" values="60,0;30,30;0,60" dur="3.5s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- 团队协作图标 -->
  <g transform="translate(200, 200)" opacity="0.08">
    <!-- 人员图标 -->
    <circle cx="0" cy="0" r="30" fill="#2c3e50"/>
    <circle cx="80" cy="0" r="30" fill="#2c3e50"/>
    <circle cx="160" cy="0" r="30" fill="#2c3e50"/>
    <circle cx="240" cy="0" r="30" fill="#2c3e50"/>
    
    <!-- 连接线 -->
    <line x1="30" y1="0" x2="50" y2="0" stroke="#2c3e50" stroke-width="4"/>
    <line x1="110" y1="0" x2="130" y2="0" stroke="#2c3e50" stroke-width="4"/>
    <line x1="190" y1="0" x2="210" y2="0" stroke="#2c3e50" stroke-width="4"/>
  </g>
  
  <!-- 中央倒计时器 -->
  <g transform="translate(960, 400)">
    <!-- 计时器外圈 -->
    <circle cx="0" cy="0" r="200" fill="none" stroke="#ecf0f1" stroke-width="20"/>
    <circle cx="0" cy="0" r="200" fill="none" stroke="#e74c3c" stroke-width="20" stroke-dasharray="1256" stroke-dashoffset="0">
      <animate attributeName="stroke-dashoffset" values="0;1256" dur="90s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 倒计时数字 -->
    <text x="0" y="30" class="timer-text">90:00</text>
  </g>
  
  <!-- 滚动提示语 -->
  <g transform="translate(960, 700)">
    <text x="0" y="0" class="tip-text" opacity="1">
      记住你的Persona，为他/她设计！
      <animate attributeName="opacity" values="1;1;0;0" dur="8s" repeatCount="indefinite"/>
    </text>
    <text x="0" y="0" class="tip-text" opacity="0">
      大胆假设，小心求证。
      <animate attributeName="opacity" values="0;0;1;1;0;0" dur="8s" begin="2s" repeatCount="indefinite"/>
    </text>
    <text x="0" y="0" class="tip-text" opacity="0">
      完成比完美更重要！
      <animate attributeName="opacity" values="0;0;1;1;0;0" dur="8s" begin="4s" repeatCount="indefinite"/>
    </text>
    <text x="0" y="0" class="tip-text" opacity="0">
      团队的力量是无穷的！
      <animate attributeName="opacity" values="0;0;1;1" dur="8s" begin="6s" repeatCount="indefinite"/>
    </text>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 950 Q 960 900 1820 950" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
</svg>
