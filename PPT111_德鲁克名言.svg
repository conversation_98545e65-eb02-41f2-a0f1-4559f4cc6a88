<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .quote-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .english-quote { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #34495e; text-anchor: middle; font-style: italic; }
      .author-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #7f8c8d; text-anchor: middle; }
    </style>
    
    <!-- 风景渐变 -->
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:0.3"/>
      <stop offset="70%" style="stop-color:#98FB98;stop-opacity:0.2"/>
      <stop offset="100%" style="stop-color:#F0E68C;stop-opacity:0.1"/>
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 风景背景 -->
  <rect width="1920" height="1080" fill="url(#skyGradient)"/>
  
  <!-- 远山轮廓 -->
  <g opacity="0.1">
    <path d="M 0 600 Q 200 500 400 550 Q 600 450 800 500 Q 1000 400 1200 450 Q 1400 350 1600 400 Q 1800 300 1920 350 L 1920 1080 L 0 1080 Z" fill="#2c3e50"/>
    
    <!-- 中景山 -->
    <path d="M 0 700 Q 300 600 600 650 Q 900 550 1200 600 Q 1500 500 1920 550 L 1920 1080 L 0 1080 Z" fill="#34495e"/>
    
    <!-- 近景山 -->
    <path d="M 0 800 Q 400 700 800 750 Q 1200 650 1920 700 L 1920 1080 L 0 1080 Z" fill="#7f8c8d"/>
  </g>
  
  <!-- 道路 -->
  <g opacity="0.08">
    <path d="M 960 1080 Q 900 800 850 600 Q 800 400 750 200" fill="none" stroke="#95a5a6" stroke-width="80"/>
    <path d="M 960 1080 Q 1020 800 1070 600 Q 1120 400 1170 200" fill="none" stroke="#95a5a6" stroke-width="80"/>
    
    <!-- 道路中线 -->
    <path d="M 960 1080 Q 960 800 960 600 Q 960 400 960 200" fill="none" stroke="#ecf0f1" stroke-width="4" stroke-dasharray="40,20"/>
  </g>
  
  <!-- 主要内容区域 -->
  <g transform="translate(960, 400)">
    <!-- 引用框 -->
    <rect x="-600" y="-150" width="1200" height="300" fill="rgba(255,255,255,0.9)" stroke="#3498db" stroke-width="3" rx="20"/>
    
    <!-- 引号装饰 -->
    <text x="-550" y="-80" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 80px; fill: #3498db; opacity: 0.3;">"</text>
    <text x="520" y="100" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 80px; fill: #3498db; opacity: 0.3;">"</text>
    
    <!-- 中文引用 -->
    <text x="0" y="-60" class="quote-text">我们无法预知未来，但我们可以创造未来。</text>
    
    <!-- 英文引用 -->
    <text x="0" y="0" class="english-quote">The best way to predict the future is to create it.</text>
    
    <!-- 作者 -->
    <text x="0" y="80" class="author-text">—— Peter Drucker</text>
  </g>
  
  <!-- 装饰性云朵 -->
  <g opacity="0.1">
    <g transform="translate(300, 150)">
      <ellipse cx="0" cy="0" rx="60" ry="30" fill="#ecf0f1"/>
      <ellipse cx="-40" cy="0" rx="40" ry="25" fill="#ecf0f1"/>
      <ellipse cx="40" cy="0" rx="50" ry="20" fill="#ecf0f1"/>
    </g>
    
    <g transform="translate(1500, 200)">
      <ellipse cx="0" cy="0" rx="80" ry="40" fill="#ecf0f1"/>
      <ellipse cx="-50" cy="0" rx="50" ry="30" fill="#ecf0f1"/>
      <ellipse cx="50" cy="0" rx="60" ry="25" fill="#ecf0f1"/>
    </g>
  </g>
</svg>
