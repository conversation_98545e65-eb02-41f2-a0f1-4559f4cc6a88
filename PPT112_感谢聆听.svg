<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .thank-you-cn { font-family: 'Microsoft YaHei', sans-serif; font-size: 72px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .thank-you-en { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #3498db; text-anchor: middle; }
      .contact-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #34495e; }
      .contact-info { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #7f8c8d; }
    </style>
    
    <!-- 简洁渐变 -->
    <linearGradient id="simpleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:0.05"/>
      <stop offset="100%" style="stop-color:#e74c3c;stop-opacity:0.05"/>
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 渐变背景 -->
  <rect width="1920" height="1080" fill="url(#simpleGradient)"/>
  
  <!-- 主要内容 -->
  <g transform="translate(960, 400)">
    <!-- 中文感谢 -->
    <text x="0" y="0" class="thank-you-cn">感谢聆听</text>
    
    <!-- 英文感谢 -->
    <text x="0" y="100" class="thank-you-en">THANK YOU</text>
  </g>
  
  <!-- 底部信息 -->
  <g transform="translate(960, 700)">
    <!-- 信息框 -->
    <rect x="-400" y="-50" width="800" height="200" fill="rgba(248,249,250,0.8)" stroke="#dee2e6" stroke-width="2" rx="15"/>
    
    <!-- 讲师信息 -->
    <text x="-350" y="-10" class="contact-title">讲师姓名：</text>
    <text x="-150" y="-10" class="contact-info">XXX</text>
    
    <!-- 联系方式 -->
    <text x="-350" y="40" class="contact-title">联系方式/邮箱：</text>
    <text x="-50" y="40" class="contact-info">XXX</text>
    
    <!-- 预留更多信息空间 -->
    <text x="0" y="100" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: #95a5a6; text-anchor: middle;">（可根据需要添加更多联系信息）</text>
  </g>
  
  <!-- 装饰性元素 -->
  <g opacity="0.1">
    <!-- 左上角装饰 -->
    <circle cx="200" cy="200" r="100" fill="#3498db"/>
    <circle cx="150" cy="150" r="60" fill="#e74c3c"/>
    
    <!-- 右下角装饰 -->
    <circle cx="1720" cy="880" r="80" fill="#f39c12"/>
    <circle cx="1650" cy="820" r="50" fill="#27ae60"/>
  </g>
  
  <!-- 简洁的装饰线 -->
  <path d="M 300 950 Q 960 900 1620 950" fill="none" stroke="#3498db" stroke-width="3" opacity="0.3"/>
</svg>
