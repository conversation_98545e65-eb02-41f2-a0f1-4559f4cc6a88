<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .instruction-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #34495e; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; }
      .time-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .english-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #95a5a6; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 背景演讲准备图片效果 -->
  <g transform="translate(1500, 300)" opacity="0.08">
    <!-- 讲台 -->
    <rect x="-100" y="100" width="200" height="20" fill="#2c3e50"/>
    <rect x="-80" y="80" width="160" height="20" fill="#2c3e50"/>
    
    <!-- 演讲者 -->
    <circle cx="0" cy="40" r="20" fill="#2c3e50"/>
    <rect x="-15" y="60" width="30" height="40" fill="#2c3e50"/>
    <path d="M -15 80 L -25 120 M 15 80 L 25 120" stroke="#2c3e50" stroke-width="3"/>
    <path d="M -15 70 L -35 90 M 15 70 L 35 90" stroke="#2c3e50" stroke-width="3"/>
    
    <!-- 观众 -->
    <g transform="translate(-150, 150)">
      <circle cx="0" cy="0" r="8" fill="#2c3e50"/>
      <circle cx="30" cy="0" r="8" fill="#2c3e50"/>
      <circle cx="60" cy="0" r="8" fill="#2c3e50"/>
    </g>
  </g>
  
  <!-- 主标题 -->
  <text x="960" y="150" class="title-text">最后准备</text>
  <text x="960" y="200" class="english-text">(Final Preparation)</text>
  
  <!-- 指令标题 -->
  <text x="200" y="300" class="instruction-title">指令：</text>
  
  <!-- 指令1 -->
  <g transform="translate(200, 360)">
    <circle cx="20" cy="20" r="15" fill="#3498db"/>
    <text x="15" y="28" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; fill: white; font-weight: bold;">1</text>
    <text x="60" y="15" class="content-text">内部推选一位"首席提案官"</text>
    <text x="60" y="45" class="content-text">(CPO - Chief Proposal Officer)。</text>
  </g>
  
  <!-- 指令2 -->
  <g transform="translate(200, 480)">
    <circle cx="20" cy="20" r="15" fill="#e74c3c"/>
    <text x="15" y="28" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; fill: white; font-weight: bold;">2</text>
    <text x="60" y="15" class="content-text">整理你们的画布，确保逻辑清晰，要点突出。</text>
  </g>
  
  <!-- 指令3 -->
  <g transform="translate(200, 560)">
    <circle cx="20" cy="20" r="15" fill="#f39c12"/>
    <text x="15" y="28" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; fill: white; font-weight: bold;">3</text>
    <text x="60" y="15" class="content-text">演练你们的5分钟提案。</text>
  </g>
  
  <!-- 时间 -->
  <g transform="translate(200, 680)">
    <rect x="0" y="0" width="1520" height="100" fill="#fff3cd" rx="15"/>
    <text x="30" y="35" class="instruction-title">时间：</text>
    <text x="150" y="35" class="time-text">10分钟</text>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 900 Q 960 850 1820 900" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
  <path d="M 200 950 Q 960 900 1720 950" fill="none" stroke="#e74c3c" stroke-width="3" opacity="0.3"/>
</svg>
