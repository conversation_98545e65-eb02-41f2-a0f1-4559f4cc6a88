<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 52px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #7f8c8d; text-anchor: middle; }
      .english-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #95a5a6; text-anchor: middle; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #34495e; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; }
      .core-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 主标题 -->
  <text x="960" y="100" class="title-text">最重要的幻灯片：你的"第一天"行动计划</text>
  
  <!-- 副标题 -->
  <text x="960" y="150" class="subtitle-text">伟大始于微小</text>
  <text x="960" y="180" class="english-text">(Great things start from small beginnings)</text>
  
  <!-- 清单项目 -->
  <!-- 第一步 -->
  <g transform="translate(200, 250)">
    <rect x="0" y="0" width="30" height="30" fill="none" stroke="#3498db" stroke-width="3" rx="5"/>
    <text x="50" y="25" class="step-text">第一步：召集一次"复盘分享会"</text>
    <text x="50" y="60" class="content-text">将本次学习的核心内容，分享给你部门的同事。</text>
  </g>
  
  <!-- 第二步 -->
  <g transform="translate(200, 350)">
    <rect x="0" y="0" width="30" height="30" fill="none" stroke="#e74c3c" stroke-width="3" rx="5"/>
    <text x="50" y="25" class="step-text">第二步：选择一个"最小切口"</text>
    <text x="50" y="60" class="content-text">不要试图解决所有问题。选择一个你最有把握、最痛的、最小的业务场景作为试点。</text>
  </g>
  
  <!-- 第三步 -->
  <g transform="translate(200, 450)">
    <rect x="0" y="0" width="30" height="30" fill="none" stroke="#f39c12" stroke-width="3" rx="5"/>
    <text x="50" y="25" class="step-text">第三步：用好一个"最小工具"</text>
    <text x="50" y="60" class="content-text">针对这个场景，尝试用本次学到的一个工具（如Persona或Journey Map）去分析它。</text>
  </g>
  
  <!-- 第四步 -->
  <g transform="translate(200, 550)">
    <rect x="0" y="0" width="30" height="30" fill="none" stroke="#27ae60" stroke-width="3" rx="5"/>
    <text x="50" y="25" class="step-text">第四步：发起一次"最小改变"</text>
    <text x="50" y="60" class="content-text">基于分析，提出一个具体的、微小的、可落地的改进建议，并推动它。</text>
  </g>
  
  <!-- 核心理念 -->
  <g transform="translate(200, 680)">
    <rect x="0" y="0" width="1520" height="120" fill="#fff3cd" rx="20"/>
    <text x="30" y="40" class="step-text">核心：</text>
    <text x="760" y="75" class="core-text">不要等待完美的时机，从一个"快速的胜利"(Quick Win)开始！</text>
  </g>
  
  <!-- 装饰性元素 -->
  <g transform="translate(1600, 300)" opacity="0.1">
    <!-- 小芽 -->
    <path d="M 0 100 Q -20 80 -10 60 Q 0 40 10 60 Q 20 80 0 100" fill="#27ae60"/>
    <rect x="-2" y="100" width="4" height="50" fill="#8b4513"/>
    
    <!-- 大树 -->
    <g transform="translate(100, 0)">
      <rect x="-5" y="100" width="10" height="50" fill="#8b4513"/>
      <circle cx="0" cy="80" r="30" fill="#27ae60"/>
      <circle cx="-20" cy="70" r="20" fill="#27ae60"/>
      <circle cx="20" cy="70" r="20" fill="#27ae60"/>
    </g>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 900 Q 960 850 1820 900" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
</svg>
