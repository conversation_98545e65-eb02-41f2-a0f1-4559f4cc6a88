<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .battle-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #34495e; text-anchor: middle; }
      .team-name { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #3498db; text-anchor: middle; }
      .task-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .task-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; }
      .english-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: #95a5a6; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 主标题 -->
  <text x="960" y="100" class="title-text">选择你们的"战役"</text>
  <text x="960" y="140" class="english-text">(Choose Your Battle)</text>
  
  <!-- 左侧A战役 -->
  <g transform="translate(480, 300)">
    <rect x="-350" y="-100" width="700" height="600" fill="#e8f4fd" stroke="#3498db" stroke-width="4" rx="20"/>
    
    <!-- APP图标 -->
    <g transform="translate(0, -50)">
      <rect x="-40" y="-40" width="80" height="80" fill="#3498db" rx="15"/>
      <text x="0" y="8" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">APP</text>
      
      <!-- 增长曲线 -->
      <path d="M -30 30 Q -10 20 10 10 Q 30 0 50 -10" fill="none" stroke="#27ae60" stroke-width="4"/>
      <circle cx="50" cy="-10" r="4" fill="#27ae60"/>
    </g>
    
    <text x="0" y="80" class="battle-title">A战役（APP运营方向）</text>
    <text x="0" y="130" class="team-name">APP增长特攻队</text>
    
    <text x="-320" y="180" class="task-title">挑战任务：</text>
    <text x="-320" y="220" class="task-text">如何有效提升"中国移动APP"内"我的权益"板块的</text>
    <text x="-320" y="250" class="task-text">用户点击率和权益核销率？让这个板块不再是沉睡</text>
    <text x="-320" y="280" class="task-text">的资产，而是提升用户粘性的利器。</text>
  </g>
  
  <!-- 右侧B战役 -->
  <g transform="translate(1440, 300)">
    <rect x="-350" y="-100" width="700" height="600" fill="#f0f8e8" stroke="#27ae60" stroke-width="4" rx="20"/>
    
    <!-- 企业微信图标 -->
    <g transform="translate(0, -50)">
      <rect x="-40" y="-40" width="80" height="80" fill="#27ae60" rx="15"/>
      <text x="0" y="-10" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: bold; fill: white; text-anchor: middle;">企微</text>
      <text x="0" y="10" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: bold; fill: white; text-anchor: middle;">社群</text>
      
      <!-- 转化箭头 -->
      <path d="M 50 -20 L 80 -20 L 75 -25 M 80 -20 L 75 -15" stroke="#e74c3c" stroke-width="3" fill="none"/>
    </g>
    
    <text x="0" y="80" class="battle-title">B战役（私域运营方向）</text>
    <text x="0" y="130" class="team-name">私域转化突击队</text>
    
    <text x="-320" y="180" class="task-title">挑战任务：</text>
    <text x="-320" y="220" class="task-text">如何利用企业微信，在3个月内，将西安某大学城的</text>
    <text x="-320" y="250" class="task-text">学生用户，从他们现在普遍使用的低价4G套餐，</text>
    <text x="-320" y="280" class="task-text">有效地转化为78元及以上的5G校园套餐？</text>
  </g>
  
  <!-- 中央VS -->
  <g transform="translate(960, 400)">
    <circle cx="0" cy="0" r="50" fill="#e74c3c"/>
    <text x="0" y="8" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: white; text-anchor: middle;">VS</text>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 900 Q 960 850 1720 900" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
</svg>
