<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #34495e; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; fill: #2c3e50; }
      .canvas-bg { fill: #fafafa; stroke: #ddd; stroke-width: 2; rx: 10; }
      .section-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 1; rx: 8; }
      .avatar-area { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 8; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">用户画像画布</text>
  
  <!-- 主画布区域 -->
  <rect x="100" y="140" width="1720" height="880" class="canvas-bg"/>
  
  <!-- 头像区域 -->
  <rect x="150" y="200" width="300" height="350" class="avatar-area"/>
  <text x="300" y="240" text-anchor="middle" class="section-title">头像</text>
  <circle cx="300" cy="320" r="60" fill="#2196f3" opacity="0.3"/>
  <text x="300" y="330" text-anchor="middle" class="content-text">贴照片或画像</text>
  <text x="300" y="420" text-anchor="middle" class="content-text">姓名：_______</text>
  <text x="300" y="450" text-anchor="middle" class="content-text">年龄：_______</text>
  <text x="300" y="480" text-anchor="middle" class="content-text">职业：_______</text>
  <text x="300" y="510" text-anchor="middle" class="content-text">地区：_______</text>
  
  <!-- 基本信息区域 -->
  <rect x="500" y="200" width="600" height="350" class="section-bg"/>
  <text x="520" y="240" class="section-title">基本信息和背景故事</text>
  <text x="520" y="280" class="content-text">家庭状况：_________________________</text>
  <text x="520" y="310" class="content-text">收入水平：_________________________</text>
  <text x="520" y="340" class="content-text">教育背景：_________________________</text>
  <text x="520" y="370" class="content-text">生活方式：_________________________</text>
  <text x="520" y="400" class="content-text">兴趣爱好：_________________________</text>
  <text x="520" y="430" class="content-text">使用设备：_________________________</text>
  <text x="520" y="480" class="section-title">人物小传（故事）：</text>
  <rect x="520" y="500" width="560" height="40" fill="white" stroke="#ddd" stroke-width="1"/>
  
  <!-- 目标区域 -->
  <rect x="1150" y="200" width="600" height="350" class="section-bg"/>
  <text x="1170" y="240" class="section-title">目标 (Goals)</text>
  <text x="1170" y="280" class="content-text">1. _________________________________</text>
  <text x="1170" y="310" class="content-text">2. _________________________________</text>
  <text x="1170" y="340" class="content-text">3. _________________________________</text>
  <text x="1170" y="390" class="section-title">痛点 (Frustrations)</text>
  <text x="1170" y="430" class="content-text">1. _________________________________</text>
  <text x="1170" y="460" class="content-text">2. _________________________________</text>
  <text x="1170" y="490" class="content-text">3. _________________________________</text>
  
  <!-- 行为特征区域 -->
  <rect x="150" y="600" width="800" height="350" class="section-bg"/>
  <text x="170" y="640" class="section-title">行为特征 & 使用场景</text>
  <text x="170" y="680" class="content-text">通信习惯：_____________________________</text>
  <text x="170" y="710" class="content-text">上网偏好：_____________________________</text>
  <text x="170" y="740" class="content-text">消费习惯：_____________________________</text>
  <text x="170" y="770" class="content-text">服务偏好：_____________________________</text>
  <text x="170" y="820" class="section-title">典型使用场景：</text>
  <rect x="170" y="840" width="760" height="80" fill="white" stroke="#ddd" stroke-width="1"/>
  
  <!-- 解决方案区域 -->
  <rect x="1000" y="600" width="750" height="350" class="section-bg"/>
  <text x="1020" y="640" class="section-title">我们如何帮助他/她？</text>
  <text x="1020" y="680" class="content-text">产品建议：_____________________________</text>
  <text x="1020" y="710" class="content-text">服务建议：_____________________________</text>
  <text x="1020" y="740" class="content-text">渠道建议：_____________________________</text>
  <text x="1020" y="770" class="content-text">沟通建议：_____________________________</text>
  <text x="1020" y="820" class="section-title">专属解决方案：</text>
  <rect x="1020" y="840" width="710" height="80" fill="white" stroke="#ddd" stroke-width="1"/>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 140 Q 200 120 300 140" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 140 Q 1720 120 1820 140" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
