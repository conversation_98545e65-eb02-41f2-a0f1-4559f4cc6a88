<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .congratulations { font-family: 'Microsoft YaHei', sans-serif; font-size: 80px; font-weight: bold; fill: #e74c3c; text-anchor: middle; }
      .main-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #34495e; text-anchor: middle; }
      .graduate-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3498db; text-anchor: middle; }
    </style>
    
    <!-- 光芒效果 -->
    <radialGradient id="lightEffect" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#f39c12;stop-opacity:0.3"/>
      <stop offset="50%" style="stop-color:#e74c3c;stop-opacity:0.2"/>
      <stop offset="100%" style="stop-color:#3498db;stop-opacity:0.1"/>
    </radialGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 光芒背景 -->
  <rect width="1920" height="1080" fill="url(#lightEffect)"/>
  
  <!-- 幕布装饰 -->
  <g opacity="0.1">
    <!-- 左侧幕布 -->
    <path d="M 0 0 Q 100 50 150 0 Q 200 50 250 0 L 250 1080 L 0 1080 Z" fill="#8b4513"/>
    
    <!-- 右侧幕布 -->
    <path d="M 1920 0 Q 1820 50 1770 0 Q 1720 50 1670 0 L 1670 1080 L 1920 1080 Z" fill="#8b4513"/>
  </g>
  
  <!-- 装饰性星星 -->
  <g opacity="0.3">
    <g transform="translate(300, 200)">
      <path d="M 0 -20 L 6 -6 L 20 -6 L 10 2 L 16 16 L 0 8 L -16 16 L -10 2 L -20 -6 L -6 -6 Z" fill="#f39c12"/>
    </g>
    <g transform="translate(1620, 250)">
      <path d="M 0 -15 L 4 -4 L 15 -4 L 7 2 L 12 12 L 0 6 L -12 12 L -7 2 L -15 -4 L -4 -4 Z" fill="#e74c3c"/>
    </g>
    <g transform="translate(400, 800)">
      <path d="M 0 -12 L 3 -3 L 12 -3 L 6 2 L 9 9 L 0 5 L -9 9 L -6 2 L -12 -3 L -3 -3 Z" fill="#3498db"/>
    </g>
    <g transform="translate(1520, 750)">
      <path d="M 0 -12 L 3 -3 L 12 -3 L 6 2 L 9 9 L 0 5 L -9 9 L -6 2 L -12 -3 L -3 -3 Z" fill="#27ae60"/>
    </g>
  </g>
  
  <!-- 主要内容 -->
  <g transform="translate(960, 400)">
    <!-- 祝贺文字 -->
    <text x="0" y="-100" class="congratulations">祝贺！</text>
    
    <!-- 主要文字 -->
    <text x="0" y="-20" class="main-text">陕西移动客户精细化运营"黄埔一期"全体学员</text>
    
    <!-- 毕业文字 -->
    <text x="0" y="60" class="graduate-text">顺利毕业！</text>
    
    <!-- 预留获奖区域 -->
    <rect x="-400" y="120" width="800" height="150" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" stroke-dasharray="10,5" rx="15"/>
    <text x="0" y="160" class="subtitle-text">（优秀学员名单）</text>
    <text x="0" y="200" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #7f8c8d; text-anchor: middle;">此处可添加获奖小组或优秀学员姓名</text>
  </g>
  
  <!-- 装饰性彩带 -->
  <g opacity="0.4">
    <path d="M 0 300 Q 480 250 960 300 Q 1440 350 1920 300" fill="none" stroke="#e74c3c" stroke-width="8"/>
    <path d="M 0 350 Q 480 300 960 350 Q 1440 400 1920 350" fill="none" stroke="#3498db" stroke-width="6"/>
    <path d="M 0 400 Q 480 350 960 400 Q 1440 450 1920 400" fill="none" stroke="#f39c12" stroke-width="4"/>
  </g>
  
  <!-- 底部装饰弧线 -->
  <path d="M 100 950 Q 960 900 1820 950" fill="none" stroke="#3498db" stroke-width="4" opacity="0.3"/>
</svg>
